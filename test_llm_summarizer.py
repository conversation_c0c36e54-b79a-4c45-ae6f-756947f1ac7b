#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试LLM步骤总结器
"""

import asyncio
import json
from llm_step_summarizer import LLMStepSummarizer
from experience_manager import ExperienceManager, QueryExperience, IterationStep, ToolExecution


async def test_storage_time_llm_summary():
    """测试存储时的LLM总结功能"""
    print("=" * 60)
    print("测试存储时的LLM总结功能")
    print("=" * 60)

    # 初始化经验管理器，启用LLM总结
    try:
        em = ExperienceManager()
        # 临时启用LLM总结
        em.experience_config["use_llm_for_step_summary"] = True
        print("✓ 经验管理器初始化成功，已启用LLM总结")
    except Exception as e:
        print(f"✗ 经验管理器初始化失败: {e}")
        return

    # 创建测试数据
    test_experience = create_test_experience()

    print(f"测试查询: {test_experience.query}")
    print(f"迭代步骤数: {len(test_experience.iterations)}")

    # 测试保存经验（会自动生成LLM总结）
    print("\n测试保存经验（包含LLM总结）...")
    success = await em.save_experience(
        query=test_experience.query,
        final_answer=test_experience.final_answer,
        execution_time=test_experience.execution_time,
        token_usage=test_experience.token_usage,
        tools_used=test_experience.tools_used,
        success=test_experience.success,
        iterations=test_experience.iterations
    )

    if success:
        print("✓ 经验保存成功")

        # 检查最新保存的经验是否包含LLM总结
        latest_exp = em.experiences[-1]
        llm_summaries = latest_exp.metadata.get("llm_step_summaries", [])

        if llm_summaries:
            print(f"✓ LLM步骤总结已保存: {len(llm_summaries)}个步骤")
            for summary in llm_summaries:
                print(f"  {summary['step']}. {summary['description']}")
        else:
            print("✗ 未找到LLM步骤总结")

        # 测试生成few-shot示例
        print("\n测试生成few-shot示例...")
        few_shot = em.generate_detailed_few_shot_example(latest_exp, 1.0)
        print("生成的few-shot示例:")
        print("-" * 60)
        print(few_shot)
        print("-" * 60)

    else:
        print("✗ 经验保存失败")


async def test_llm_summarizer():
    """测试LLM步骤总结器"""
    print("=" * 60)
    print("测试LLM步骤总结器")
    print("=" * 60)

    # 初始化总结器
    try:
        summarizer = LLMStepSummarizer()
        print("✓ LLM步骤总结器初始化成功")
    except Exception as e:
        print(f"✗ LLM步骤总结器初始化失败: {e}")
        return
    
    # 检查是否有经验数据
    if not summarizer.experience_manager.experiences:
        print("✗ 没有找到经验数据，请先运行一些查询生成经验")
        return
    
    # 选择第一个成功的经验进行测试
    test_experience = None
    for exp in summarizer.experience_manager.experiences:
        if exp.success and exp.iterations:
            test_experience = exp
            break
    
    if not test_experience:
        print("✗ 没有找到包含迭代步骤的成功经验")
        return
    
    print(f"✓ 找到测试经验: {test_experience.query[:50]}...")
    print(f"  - 执行时间: {test_experience.execution_time:.2f}s")
    print(f"  - 迭代次数: {test_experience.total_iterations}")
    print(f"  - 使用工具: {len(test_experience.tools_used)}")
    
    # 测试步骤总结
    print("\n测试步骤总结...")
    try:
        step_summaries = await summarizer.summarize_experience_steps(test_experience)
        
        if step_summaries:
            print("✓ 步骤总结生成成功:")
            for summary in step_summaries:
                print(f"  {summary['step']}. {summary['description']}")
                print(f"     {summary['action_json']}")
        else:
            print("✗ 步骤总结生成失败")
            return
            
    except Exception as e:
        print(f"✗ 步骤总结生成异常: {e}")
        return
    
    # 测试完整few-shot示例生成
    print("\n测试完整few-shot示例生成...")
    try:
        improved_example = await summarizer.generate_improved_few_shot_example(test_experience, 1.0)
        
        print("✓ 改进的few-shot示例生成成功:")
        print("-" * 60)
        print(improved_example)
        print("-" * 60)
        
    except Exception as e:
        print(f"✗ few-shot示例生成异常: {e}")
        return
    
    # 对比传统方法
    print("\n对比传统方法...")
    try:
        traditional_example = summarizer.experience_manager.generate_detailed_few_shot_example(test_experience, 1.0)
        
        print("传统方法生成的示例:")
        print("-" * 60)
        print(traditional_example)
        print("-" * 60)
        
    except Exception as e:
        print(f"传统方法生成异常: {e}")
    
    print("\n" + "=" * 60)
    print("测试完成")
    print("=" * 60)


async def test_batch_improvement():
    """测试批量改进功能"""
    print("=" * 60)
    print("测试批量改进功能")
    print("=" * 60)
    
    summarizer = LLMStepSummarizer()
    
    # 只改进前3个经验（避免API调用过多）
    limited_experiences = []
    count = 0
    for exp in summarizer.experience_manager.experiences:
        if exp.success and exp.iterations and count < 3:
            limited_experiences.append(exp)
            count += 1
    
    if not limited_experiences:
        print("没有找到可改进的经验")
        return
    
    print(f"找到 {len(limited_experiences)} 个经验进行改进")
    
    # 临时替换经验列表
    original_experiences = summarizer.experience_manager.experiences
    summarizer.experience_manager.experiences = limited_experiences
    
    try:
        improved = await summarizer.batch_improve_experiences("test_improved_experiences.json")
        print(f"✓ 成功改进 {len(improved)} 个经验")
        
        # 显示第一个改进的示例
        if improved:
            print("\n第一个改进的示例:")
            print("-" * 60)
            print(improved[0]["improved_few_shot"])
            print("-" * 60)
            
    except Exception as e:
        print(f"✗ 批量改进失败: {e}")
    finally:
        # 恢复原始经验列表
        summarizer.experience_manager.experiences = original_experiences


def compare_methods():
    """对比不同方法的输出"""
    print("=" * 60)
    print("对比不同方法的输出")
    print("=" * 60)
    
    em = ExperienceManager()
    
    if not em.experiences:
        print("没有经验数据可供对比")
        return
    
    # 选择第一个有效经验
    test_exp = None
    for exp in em.experiences:
        if exp.success and exp.iterations:
            test_exp = exp
            break
    
    if not test_exp:
        print("没有找到有效的经验数据")
        return
    
    print(f"测试经验: {test_exp.query}")
    print(f"迭代步骤数: {len(test_exp.iterations)}")
    
    # 传统方法
    print("\n=== 传统方法 ===")
    traditional = em.generate_detailed_few_shot_example(test_exp, 1.0)
    print(traditional)
    
    # 显示原始迭代数据
    print("\n=== 原始迭代数据 ===")
    for i, iteration in enumerate(test_exp.iterations, 1):
        print(f"迭代 {i}:")
        print(f"  思考: {iteration.thought[:100]}...")
        print(f"  动作: {iteration.action}")
        print(f"  观察: {iteration.observation[:100]}...")
        if iteration.tool_executions:
            for tool_exec in iteration.tool_executions:
                print(f"  工具: {tool_exec.tool_name}")
                print(f"  参数: {tool_exec.parameters}")
        print()


def create_test_experience() -> QueryExperience:
    """创建测试经验数据"""
    # 创建工具执行记录
    tool_executions = [
        ToolExecution(
            tool_name="stats-query-mcp_mcp_tool_setup_api_connection",
            parameters={"url": "https://192.168.163.209:8080/"},
            response='{"success": true, "message": "API连接设置成功"}',
            execution_time=2.1,
            success=True,
            timestamp="2025-07-17 10:00:00"
        ),
        ToolExecution(
            tool_name="stats-query-mcp_mcp_tool_get_config",
            parameters={"config_type": "netlink", "netlink_id": 64},
            response='{"success": true, "xml": "<netlink><link id=\\"1\\" name=\\"链路一\\"/>"}',
            execution_time=1.5,
            success=True,
            timestamp="2025-07-17 10:00:02"
        ),
        ToolExecution(
            tool_name="stats-query-mcp_mcp_tool_query_statistics_table",
            parameters={
                "table": "summary",
                "begintime": "2025-07-17 00:00:00",
                "endtime": "2025-07-17 23:59:59",
                "fields": "total_byte,total_packet,total_bitps",
                "keys": "time",
                "netlink": 1
            },
            response='{"success": true, "data": "total_byte,total_packet,total_bitps\\n923644955558,**********,85522681.070180"}',
            execution_time=15.2,
            success=True,
            timestamp="2025-07-17 10:00:05"
        )
    ]

    # 创建迭代步骤
    iterations = [
        IterationStep(
            iteration=1,
            thought="我需要先连接到API服务器",
            action="stats-query-mcp_mcp_tool_setup_api_connection",
            action_input={"url": "https://192.168.163.209:8080/"},
            observation="API连接成功",
            tool_executions=[tool_executions[0]],
            duration=2.1
        ),
        IterationStep(
            iteration=2,
            thought="现在需要获取链路配置信息",
            action="stats-query-mcp_mcp_tool_get_config",
            action_input={"config_type": "netlink", "netlink_id": 64},
            observation="获取到链路配置，链路一的ID是1",
            tool_executions=[tool_executions[1]],
            duration=1.5
        ),
        IterationStep(
            iteration=3,
            thought="现在可以查询统计数据了",
            action="stats-query-mcp_mcp_tool_query_statistics_table",
            action_input={
                "table": "summary",
                "begintime": "2025-07-17 00:00:00",
                "endtime": "2025-07-17 23:59:59",
                "fields": "total_byte,total_packet,total_bitps",
                "keys": "time",
                "netlink": 1
            },
            observation="查询成功，获取到统计数据",
            tool_executions=[tool_executions[2]],
            duration=15.2
        )
    ]

    # 创建查询经验
    import time
    unique_query = f"查询192.168.163.209 上链路名称为链路一的2025-07-17一天的概要统计 (测试时间: {int(time.time())})"
    return QueryExperience(
        query=unique_query,
        query_hash=f"test_hash_{int(time.time())}",
        final_answer='{"success": true, "error_code": 0, "message": "查询成功", "data": "total_byte,total_packet,total_bitps\\n923644955558,**********,85522681.070180"}',
        execution_time=21.3,
        token_usage=1250,
        tools_used=[
            "stats-query-mcp_mcp_tool_setup_api_connection",
            "stats-query-mcp_mcp_tool_get_config",
            "stats-query-mcp_mcp_tool_query_statistics_table"
        ],
        success=True,
        timestamp="2025-07-17 10:00:00",
        iterations=iterations,
        total_iterations=3,
        embedding=None,
        metadata={"model": "gpt-4", "provider": "openai"}
    )


async def main():
    """主函数"""
    import sys

    if len(sys.argv) < 2:
        print("用法:")
        print("  python test_llm_summarizer.py storage   # 测试存储时LLM总结")
        print("  python test_llm_summarizer.py test      # 测试LLM总结器")
        print("  python test_llm_summarizer.py batch     # 测试批量改进")
        print("  python test_llm_summarizer.py compare   # 对比不同方法")
        return

    command = sys.argv[1]

    if command == "storage":
        await test_storage_time_llm_summary()
    elif command == "test":
        await test_llm_summarizer()
    elif command == "batch":
        await test_batch_improvement()
    elif command == "compare":
        compare_methods()
    else:
        print("无效的命令")


if __name__ == "__main__":
    asyncio.run(main())
