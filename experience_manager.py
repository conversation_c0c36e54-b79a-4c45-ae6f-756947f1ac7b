import json
import os
import time
import hashlib
import logging
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict
from datetime import datetime
import numpy as np
from sklearn.metrics.pairwise import cosine_similarity
import requests


@dataclass
class ToolExecution:
    """工具执行记录"""
    tool_name: str
    parameters: Dict[str, Any]
    response: str
    execution_time: float
    success: bool
    timestamp: str
    error_message: Optional[str] = None

@dataclass
class IterationStep:
    """迭代步骤记录"""
    iteration: int
    thought: str
    action: Optional[str]
    action_input: Optional[Dict[str, Any]]
    observation: str
    tool_executions: List[ToolExecution]
    duration: float

@dataclass
class QueryExperience:
    """查询经验数据结构"""
    query: str
    query_hash: str
    final_answer: str
    execution_time: float
    token_usage: int
    tools_used: List[str]
    success: bool
    timestamp: str
    # 新增详细过程信息
    iterations: List[IterationStep]
    total_iterations: int
    embedding: Optional[List[float]] = None
    metadata: Optional[Dict[str, Any]] = None


class ExperienceManager:
    """经验管理器 - 保存和检索成功的查询经验"""
    
    def __init__(self, config_path: str = "experience_config.json"):
        """初始化经验管理器
        
        Args:
            config_path: 配置文件路径
        """
        self.config = self._load_config(config_path)
        self.experience_config = self.config["experience_manager"]
        self.embedding_config = self.config["embedding_config"]
        self.text_similarity_config = self.config["text_similarity_config"]
        
        # 设置日志
        self._setup_logging()
        
        # 经验存储文件路径
        self.experience_file = self.experience_config["experience_file"]
        
        # 加载现有经验
        self.experiences: List[QueryExperience] = self._load_experiences()
        
        self.logger.info(f"经验管理器初始化完成，已加载 {len(self.experiences)} 条经验")
    
    def _load_config(self, config_path: str) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            raise FileNotFoundError(f"配置文件未找到: {config_path}")
        except json.JSONDecodeError as e:
            raise ValueError(f"配置文件格式错误: {e}")
    
    def _setup_logging(self):
        """设置日志"""
        log_config = self.config["logging_config"]
        
        # 创建logger
        self.logger = logging.getLogger("ExperienceManager")
        self.logger.setLevel(getattr(logging, log_config["log_level"]))
        
        # 创建文件处理器
        handler = logging.FileHandler(
            log_config["log_file"], 
            encoding='utf-8'
        )
        
        # 设置格式
        formatter = logging.Formatter(log_config["log_format"])
        handler.setFormatter(formatter)
        
        # 避免重复添加处理器
        if not self.logger.handlers:
            self.logger.addHandler(handler)
    
    def _load_experiences(self) -> List[QueryExperience]:
        """从文件加载经验数据"""
        if not os.path.exists(self.experience_file):
            return []

        try:
            with open(self.experience_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
                experiences = []
                for item in data:
                    # 转换iterations字典为IterationStep对象
                    iterations = []
                    if 'iterations' in item and item['iterations']:
                        for iter_data in item['iterations']:
                            # 转换tool_executions字典为ToolExecution对象
                            tool_executions = []
                            if 'tool_executions' in iter_data and iter_data['tool_executions']:
                                for tool_data in iter_data['tool_executions']:
                                    tool_exec = ToolExecution(**tool_data)
                                    tool_executions.append(tool_exec)

                            # 创建IterationStep对象
                            iter_step = IterationStep(
                                iteration=iter_data.get('iteration', 0),
                                thought=iter_data.get('thought', ''),
                                action=iter_data.get('action'),
                                action_input=iter_data.get('action_input'),
                                observation=iter_data.get('observation', ''),
                                tool_executions=tool_executions,
                                duration=iter_data.get('duration', 0.0)
                            )
                            iterations.append(iter_step)

                    # 创建QueryExperience对象
                    experience = QueryExperience(
                        query=item.get('query', ''),
                        query_hash=item.get('query_hash', ''),
                        final_answer=item.get('final_answer', ''),
                        execution_time=item.get('execution_time', 0.0),
                        token_usage=item.get('token_usage', 0),
                        tools_used=item.get('tools_used', []),
                        success=item.get('success', False),
                        timestamp=item.get('timestamp', ''),
                        iterations=iterations,
                        total_iterations=item.get('total_iterations', len(iterations)),
                        embedding=item.get('embedding'),
                        metadata=item.get('metadata', {})
                    )
                    experiences.append(experience)
                return experiences
        except Exception as e:
            self.logger.error(f"加载经验文件失败: {e}")
            return []
    
    def _save_experiences(self):
        """保存经验数据到文件"""
        try:
            # 限制经验数量
            max_experiences = self.experience_config["max_experiences"]
            if len(self.experiences) > max_experiences:
                # 保留最新的经验
                self.experiences = self.experiences[-max_experiences:]

            # 转换为字典格式保存，确保所有嵌套对象都被正确序列化
            data = []
            for exp in self.experiences:
                exp_dict = asdict(exp)
                data.append(exp_dict)

            with open(self.experience_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)

            self.logger.info(f"已保存 {len(self.experiences)} 条经验到文件")

        except Exception as e:
            self.logger.error(f"保存经验文件失败: {e}")
    
    def _generate_query_hash(self, query: str) -> str:
        """生成查询的哈希值"""
        return hashlib.md5(query.encode('utf-8')).hexdigest()
    
    async def _get_embedding(self, text: str) -> Optional[List[float]]:
        """获取文本的embedding向量"""
        if not self.experience_config["use_embedding"]:
            return None
        
        try:
            # 使用配置的embedding服务
            headers = {
                "Authorization": f"Bearer {self.embedding_config['api_key']}",
                "Content-Type": "application/json"
            }
            
            data = {
                "model": self.embedding_config["model"],
                "input": text
            }
            
            response = requests.post(
                self.embedding_config["base_url"],
                headers=headers,
                json=data,
                timeout=self.embedding_config["timeout"]
            )
            
            if response.status_code == 200:
                result = response.json()
                return result["data"][0]["embedding"]
            else:
                self.logger.error(f"获取embedding失败: {response.status_code}")
                return None
                
        except Exception as e:
            self.logger.error(f"获取embedding异常: {e}")
            return None
    
    def _calculate_text_similarity(self, query1: str, query2: str) -> float:
        """计算文本相似度（基于字符和长度）"""
        config = self.text_similarity_config
        
        # Jaccard相似度
        set1 = set(query1.lower())
        set2 = set(query2.lower())
        jaccard = len(set1 & set2) / len(set1 | set2) if set1 | set2 else 0
        
        # 字符相似度
        common_chars = sum(1 for c in query1.lower() if c in query2.lower())
        char_sim = common_chars / max(len(query1), len(query2)) if max(len(query1), len(query2)) > 0 else 0
        
        # 长度相似度
        len_sim = 1 - abs(len(query1) - len(query2)) / max(len(query1), len(query2)) if max(len(query1), len(query2)) > 0 else 1
        
        # 加权计算
        similarity = (
            jaccard * config["jaccard_weight"] +
            char_sim * config["char_weight"] +
            len_sim * config["length_weight"]
        )
        
        return similarity
    
    def _calculate_embedding_similarity(self, embedding1: List[float], embedding2: List[float]) -> float:
        """计算embedding向量相似度"""
        try:
            vec1 = np.array(embedding1).reshape(1, -1)
            vec2 = np.array(embedding2).reshape(1, -1)
            return cosine_similarity(vec1, vec2)[0][0]
        except Exception as e:
            self.logger.error(f"计算embedding相似度失败: {e}")
            return 0.0

    async def save_experience(
        self,
        query: str,
        final_answer: str,
        execution_time: float,
        token_usage: int,
        tools_used: List[str],
        success: bool = True,
        iterations: Optional[List[IterationStep]] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> bool:
        """保存查询经验

        Args:
            query: 用户查询
            final_answer: 最终答案
            execution_time: 执行时间（秒）
            token_usage: token使用量
            tools_used: 使用的工具列表
            success: 是否成功
            metadata: 额外的元数据

        Returns:
            是否保存成功
        """
        if not self.experience_config["enabled"]:
            return False

        # 检查是否满足保存条件
        if execution_time < self.experience_config["min_execution_time"]:
            self.logger.info(f"执行时间过短，跳过保存: {execution_time}s < {self.experience_config['min_execution_time']}s")
            print(f"[EXPERIENCE] 执行时间过短，跳过保存: {execution_time}s < {self.experience_config['min_execution_time']}s")
            return False

        if token_usage < self.experience_config["min_token_usage"]:
            self.logger.info(f"token使用量过少，跳过保存: {token_usage} < {self.experience_config['min_token_usage']}")
            print(f"[EXPERIENCE] token使用量过少，跳过保存: {token_usage} < {self.experience_config['min_token_usage']}")
            return False

        if not success:
            self.logger.info("查询失败，跳过保存")
            print(f"[EXPERIENCE] 查询失败，跳过保存")
            return False

        try:
            # 生成查询哈希
            query_hash = self._generate_query_hash(query)

            # 检查是否已存在相同的查询
            existing_exp = next((exp for exp in self.experiences if exp.query_hash == query_hash), None)
            if existing_exp:
                self.logger.info(f"查询已存在，跳过保存: {query[:50]}...")
                print(f"[EXPERIENCE] 查询已存在，跳过保存: {query[:50]}...")
                return False

            # 获取embedding（如果启用）
            embedding = None
            if self.experience_config["use_embedding"]:
                embedding = await self._get_embedding(query)

            # 生成LLM步骤总结（如果启用且有迭代步骤）
            step_summaries = None
            if (iterations and
                self.experience_config.get("use_llm_for_step_summary", False)):
                try:
                    print(f"[EXPERIENCE] 开始生成LLM步骤总结，迭代步骤数: {len(iterations)}")
                    step_summaries = await self._generate_llm_step_summaries(query, iterations)
                    if step_summaries:
                        self.logger.info(f"成功生成LLM步骤总结: {len(step_summaries)}个步骤")
                        print(f"[EXPERIENCE] 成功生成LLM步骤总结: {len(step_summaries)}个步骤")
                    else:
                        print(f"[EXPERIENCE] LLM步骤总结为空")
                except Exception as e:
                    self.logger.warning(f"LLM步骤总结生成失败: {e}")
                    print(f"[EXPERIENCE] LLM步骤总结生成失败: {e}")
                    import traceback
                    traceback.print_exc()
            else:
                if not iterations:
                    print(f"[EXPERIENCE] 没有迭代步骤，跳过LLM总结")
                elif not self.experience_config.get("use_llm_for_step_summary", False):
                    print(f"[EXPERIENCE] LLM步骤总结未启用")

            # 准备metadata
            final_metadata = metadata or {}
            if step_summaries:
                final_metadata["llm_step_summaries"] = step_summaries

            # 创建经验对象
            experience = QueryExperience(
                query=query,
                query_hash=query_hash,
                final_answer=final_answer,
                execution_time=execution_time,
                token_usage=token_usage,
                tools_used=tools_used,
                success=success,
                timestamp=datetime.now().isoformat(),
                iterations=iterations or [],
                total_iterations=len(iterations) if iterations else 0,
                embedding=embedding,
                metadata=final_metadata
            )

            # 添加到经验列表
            self.experiences.append(experience)

            # 自动保存（如果启用）
            if self.experience_config["auto_save"]:
                self._save_experiences()

            self.logger.info(f"成功保存查询经验: {query[:50]}...")
            print(f"[EXPERIENCE] 成功保存查询经验: {query[:50]}...")
            print(f"[EXPERIENCE] 经验详情: 执行时间={execution_time:.2f}s, token={token_usage}, 工具={tools_used}")
            return True

        except Exception as e:
            self.logger.error(f"保存经验失败: {e}")
            return False

    async def find_similar_experiences(self, query: str, top_k: Optional[int] = None) -> List[Tuple[QueryExperience, float]]:
        """查找相似的查询经验

        Args:
            query: 用户查询
            top_k: 返回的最大数量，默认使用配置中的值

        Returns:
            相似经验列表，每个元素为 (经验, 相似度分数)
        """
        if not self.experience_config["enabled"] or not self.experiences:
            return []

        if top_k is None:
            top_k = self.experience_config["top_k_similar"]

        try:
            similarities = []
            query_embedding = None

            # 获取查询的embedding（如果启用）
            if self.experience_config["use_embedding"]:
                query_embedding = await self._get_embedding(query)

            for experience in self.experiences:
                if not experience.success:
                    continue

                similarity = 0.0

                # 使用embedding计算相似度
                if (query_embedding and experience.embedding and
                    self.experience_config["use_embedding"]):
                    similarity = self._calculate_embedding_similarity(query_embedding, experience.embedding)
                else:
                    # 使用文本相似度
                    similarity = self._calculate_text_similarity(query, experience.query)

                # 检查相似度阈值
                if similarity >= self.experience_config["similarity_threshold"]:
                    similarities.append((experience, similarity))

            # 按相似度排序并返回top_k
            similarities.sort(key=lambda x: x[1], reverse=True)
            result = similarities[:top_k]

            self.logger.info(f"找到 {len(result)} 个相似经验，查询: {query[:50]}...")
            return result

        except Exception as e:
            self.logger.error(f"查找相似经验失败: {e}")
            return []

    def get_experience_summary(self) -> Dict[str, Any]:
        """获取经验统计摘要"""
        if not self.experiences:
            return {
                "total_experiences": 0,
                "success_rate": 0.0,
                "avg_execution_time": 0.0,
                "avg_token_usage": 0.0,
                "most_used_tools": []
            }

        successful_experiences = [exp for exp in self.experiences if exp.success]

        # 统计工具使用频率
        tool_usage = {}
        for exp in successful_experiences:
            for tool in exp.tools_used:
                tool_usage[tool] = tool_usage.get(tool, 0) + 1

        most_used_tools = sorted(tool_usage.items(), key=lambda x: x[1], reverse=True)[:5]

        return {
            "total_experiences": len(self.experiences),
            "successful_experiences": len(successful_experiences),
            "success_rate": len(successful_experiences) / len(self.experiences) if self.experiences else 0.0,
            "avg_execution_time": sum(exp.execution_time for exp in successful_experiences) / len(successful_experiences) if successful_experiences else 0.0,
            "avg_token_usage": sum(exp.token_usage for exp in successful_experiences) / len(successful_experiences) if successful_experiences else 0.0,
            "most_used_tools": most_used_tools
        }

    def manual_save(self):
        """手动保存经验到文件"""
        self._save_experiences()

    def clear_experiences(self):
        """清空所有经验"""
        self.experiences.clear()
        self._save_experiences()
        self.logger.info("已清空所有经验")

    def generate_detailed_few_shot_example(self, experience: 'QueryExperience', similarity: float) -> str:
        """生成详细的few-shot示例，包含执行过程

        Args:
            experience: 经验对象
            similarity: 相似度

        Returns:
            格式化的few-shot示例
        """
        example = f"## Example\n"
        example += f"Example 1：{experience.query}\n"
        example += f"Step:\n"

        # 检查是否有存储的LLM步骤总结
        llm_summaries = experience.metadata.get("llm_step_summaries", [])

        if llm_summaries:
            # 使用存储的LLM总结
            for summary in llm_summaries:
                example += f"{summary['step']}. {summary['description']}\n"
                example += f"{summary['action_json']}\n"
            print(f"[EXPERIENCE] 使用存储的LLM步骤总结: {len(llm_summaries)}个步骤")
        else:
            # 回退到传统方法：基于迭代数据生成步骤描述
            step_counter = 1
            for iteration in experience.iterations:
                if iteration.action and iteration.tool_executions:
                    for tool_exec in iteration.tool_executions:
                        if tool_exec.success:
                            # 基于上下文生成步骤描述
                            step_description = self._generate_step_description(
                                tool_exec, iteration, experience.query
                            )

                            example += f"{step_counter}. {step_description}\n"

                            # 格式化参数
                            action_dict = {
                                'action': tool_exec.tool_name,
                                'action_input': tool_exec.parameters
                            }
                            example += f"{action_dict}\n"
                            step_counter += 1
            print(f"[EXPERIENCE] 使用传统方法生成步骤描述")

        # 添加简化的最终答案
        final_answer = self._extract_key_result(experience.final_answer)

        example += f"\n**Expected Result**: {final_answer}\n\n"
        example += f"**Execution Tips**: This query took {experience.execution_time:.1f}s with {experience.total_iterations} iterations.\n\n"

        return example

    def _generate_step_description(self, tool_exec: 'ToolExecution', iteration: 'IterationStep', query: str) -> str:
        """基于上下文生成步骤描述

        Args:
            tool_exec: 工具执行信息
            iteration: 迭代步骤信息
            query: 原始查询

        Returns:
            步骤描述
        """
        tool_name = tool_exec.tool_name
        parameters = tool_exec.parameters
        thought = iteration.thought
        observation = iteration.observation

        # 基于工具名称和参数生成描述
        if "setup_api_connection" in tool_name:
            return "登录API服务器"
        elif "get_config" in tool_name:
            config_type = parameters.get("config_type", "")
            if config_type == "netlink":
                return "查询链路配置获取链路ID信息"
            return f"获取{config_type}配置信息"
        elif "list_statistics_tables" in tool_name:
            return "查询可用的统计表列表"
        elif "list_table_fields" in tool_name:
            table_name = parameters.get("table_name", "")
            return f"查询{table_name}表的字段信息"
        elif "query_statistics_table" in tool_name:
            table = parameters.get("table", "")
            fields = parameters.get("fields", "")
            return f"查询{table}表的统计数据"
        elif "disconnect_api" in tool_name:
            return "断开API连接"
        elif "get_time_examples" in tool_name:
            return "获取时间格式示例"
        elif "get_api_error_codes" in tool_name:
            return "获取API错误码说明"
        elif "get_table_description" in tool_name:
            return "获取表描述信息"
        else:
            # 基于思考过程和观察结果推断
            if thought and len(thought) > 10:
                # 从思考过程中提取关键信息
                if "登录" in thought or "连接" in thought:
                    return "建立API连接"
                elif "表" in thought and "字段" in thought:
                    return "查询表字段信息"
                elif "配置" in thought:
                    return "获取配置信息"
                elif "查询" in thought and "统计" in thought:
                    return "执行统计数据查询"

            # 默认描述
            return f"执行{tool_name.split('_')[-1]}操作"

    def _get_tool_description(self, tool_name: str) -> str:
        """获取工具的中文描述 - 简化版本，仅用于向后兼容

        Args:
            tool_name: 工具名称

        Returns:
            简化的工具名称
        """
        # 简化工具名称显示
        if "mcp_tool_" in tool_name:
            return tool_name.split("mcp_tool_")[-1]
        return tool_name

    def export_experiences(self, export_path: str) -> bool:
        """导出经验到指定文件

        Args:
            export_path: 导出文件路径

        Returns:
            是否导出成功
        """
        try:
            data = [asdict(exp) for exp in self.experiences]
            with open(export_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)

            self.logger.info(f"成功导出 {len(self.experiences)} 条经验到 {export_path}")
            return True

        except Exception as e:
            self.logger.error(f"导出经验失败: {e}")
            return False

    def generate_execution_guidance(self, query: str, similar_experiences: List[Tuple['QueryExperience', float]]) -> str:
        """基于相似经验生成执行指导

        Args:
            query: 当前查询
            similar_experiences: 相似经验列表

        Returns:
            执行指导文本
        """
        if not similar_experiences:
            return ""

        guidance = "## Execution Guidance Based on Similar Experiences\n\n"

        # 分析最相似的经验
        best_exp, best_similarity = similar_experiences[0]

        guidance += f"Based on a similar query (similarity: {best_similarity:.2f}), here's the recommended approach:\n\n"

        # 推荐的工具执行顺序
        if best_exp.iterations:
            guidance += "### Recommended Tool Execution Sequence:\n"
            step_num = 1
            for iteration in best_exp.iterations:
                if iteration.action and iteration.tool_executions:
                    for tool_exec in iteration.tool_executions:
                        if tool_exec.success:
                            guidance += f"{step_num}. **{self._get_tool_description(tool_exec.tool_name)}**\n"
                            guidance += f"   - Tool: `{tool_exec.tool_name}`\n"
                            guidance += f"   - Key parameters: {self._format_key_parameters(tool_exec.parameters)}\n"
                            if tool_exec.execution_time > 0:
                                guidance += f"   - Expected duration: ~{tool_exec.execution_time:.1f}s\n"
                            guidance += "\n"
                            step_num += 1

        # 性能预期
        guidance += "### Performance Expectations:\n"
        guidance += f"- Estimated total execution time: ~{best_exp.execution_time:.1f}s\n"
        guidance += f"- Expected iterations: {best_exp.total_iterations}\n"
        guidance += f"- Token usage estimate: ~{best_exp.token_usage}\n\n"

        # 常见问题和注意事项
        guidance += "### Important Notes:\n"
        guidance += "- Ensure API connection is established first\n"
        guidance += "- Verify table and field names before querying\n"
        guidance += "- Check time format requirements\n"
        guidance += "- Handle error codes appropriately\n\n"

        return guidance

    def _format_key_parameters(self, parameters: Dict[str, Any]) -> str:
        """格式化关键参数显示

        Args:
            parameters: 参数字典

        Returns:
            格式化的参数字符串
        """
        if not parameters:
            return "None"

        # 只显示关键参数，避免过长
        key_params = {}
        important_keys = ['url', 'table', 'begintime', 'endtime', 'fields', 'keys', 'config_type', 'netlink_id']

        for key in important_keys:
            if key in parameters:
                value = parameters[key]
                if isinstance(value, str) and len(value) > 50:
                    value = value[:50] + "..."
                key_params[key] = value

        if not key_params:
            # 如果没有重要参数，显示前3个参数
            items = list(parameters.items())[:3]
            for key, value in items:
                if isinstance(value, str) and len(value) > 30:
                    value = value[:30] + "..."
                key_params[key] = value

        return str(key_params)

    def analyze_query_patterns(self) -> Dict[str, Any]:
        """分析查询模式，提供优化建议

        Returns:
            查询模式分析结果
        """
        if not self.experiences:
            return {"message": "No experiences to analyze"}

        successful_experiences = [exp for exp in self.experiences if exp.success]

        # 分析工具使用模式
        tool_sequences = []
        for exp in successful_experiences:
            if exp.tools_used:
                tool_sequences.append(exp.tools_used)

        # 找出最常见的工具序列
        from collections import Counter
        sequence_counter = Counter(tuple(seq) for seq in tool_sequences)
        common_sequences = sequence_counter.most_common(5)

        # 分析执行时间模式
        execution_times = [exp.execution_time for exp in successful_experiences]
        avg_time = sum(execution_times) / len(execution_times) if execution_times else 0

        # 分析迭代次数模式
        iterations = [exp.total_iterations for exp in successful_experiences]
        avg_iterations = sum(iterations) / len(iterations) if iterations else 0

        # 分析失败模式
        failed_experiences = [exp for exp in self.experiences if not exp.success]
        failure_rate = len(failed_experiences) / len(self.experiences) if self.experiences else 0

        return {
            "total_experiences": len(self.experiences),
            "success_rate": 1 - failure_rate,
            "average_execution_time": avg_time,
            "average_iterations": avg_iterations,
            "common_tool_sequences": [
                {"sequence": list(seq), "count": count}
                for seq, count in common_sequences
            ],
            "performance_insights": {
                "fastest_query_time": min(execution_times) if execution_times else 0,
                "slowest_query_time": max(execution_times) if execution_times else 0,
                "most_efficient_iterations": min(iterations) if iterations else 0,
                "least_efficient_iterations": max(iterations) if iterations else 0
            }
        }

    def get_optimization_suggestions(self, query: str) -> List[str]:
        """基于历史经验提供优化建议

        Args:
            query: 当前查询

        Returns:
            优化建议列表
        """
        suggestions = []

        if not self.experiences:
            return ["No historical data available for optimization suggestions"]

        # 分析相似查询的成功模式
        successful_experiences = [exp for exp in self.experiences if exp.success]

        if successful_experiences:
            # 建议最常用的工具序列
            tool_usage = {}
            for exp in successful_experiences:
                for tool in exp.tools_used:
                    tool_usage[tool] = tool_usage.get(tool, 0) + 1

            most_used_tools = sorted(tool_usage.items(), key=lambda x: x[1], reverse=True)[:3]
            if most_used_tools:
                suggestions.append(f"Consider using these commonly successful tools: {', '.join([tool for tool, _ in most_used_tools])}")

            # 基于执行时间的建议
            avg_time = sum(exp.execution_time for exp in successful_experiences) / len(successful_experiences)
            if avg_time > 10:
                suggestions.append("Consider optimizing query parameters to reduce execution time")

            # 基于迭代次数的建议
            avg_iterations = sum(exp.total_iterations for exp in successful_experiences) / len(successful_experiences)
            if avg_iterations > 3:
                suggestions.append("Try to be more specific in your initial query to reduce iterations")

        return suggestions if suggestions else ["No specific optimization suggestions available"]

    async def _generate_llm_step_summaries(self, query: str, iterations: List['IterationStep']) -> List[Dict[str, Any]]:
        """生成LLM步骤总结

        Args:
            query: 用户查询
            iterations: 迭代步骤列表

        Returns:
            步骤总结列表
        """
        try:
            prompt = self._build_step_summary_prompt(query, iterations)
            return await self._call_llm_for_step_summary(prompt)
        except Exception as e:
            self.logger.error(f"生成LLM步骤总结失败: {e}")
            import traceback
            traceback.print_exc()
            return []

    def _build_step_summary_prompt(self, query: str, iterations: List['IterationStep']) -> str:
        """构建用于LLM总结步骤的提示词"""
        prompt = f"""请分析以下网络统计查询的执行过程，为每个步骤生成简洁准确的中文描述。

用户查询: {query}

执行步骤详情:
"""

        step_num = 1
        for iteration in iterations:
            if iteration.action and iteration.tool_executions:
                for tool_exec in iteration.tool_executions:
                    if tool_exec.success:
                        prompt += f"\n=== 步骤 {step_num} ===\n"
                        prompt += f"工具名称: {tool_exec.tool_name}\n"
                        prompt += f"输入参数: {json.dumps(tool_exec.parameters, ensure_ascii=False)}\n"
                        prompt += f"AI思考: {iteration.thought}\n"
                        prompt += f"执行结果: {tool_exec.response[:300]}...\n"
                        prompt += f"观察总结: {iteration.observation}\n"
                        step_num += 1

        prompt += """

请为每个步骤生成:
1. 一句话的中文描述，准确说明这个步骤的目的和作用
2. 对应的完整action JSON格式

要求:
- 描述要准确反映步骤的实际作用，不要使用通用描述
- 根据参数内容推断具体操作（如查询特定表、获取特定配置等）
- 保持原有的action和action_input格式不变
- 按执行顺序排列

输出格式（严格按照此JSON格式）:
```json
[
  {
    "step": 1,
    "description": "登录到***************的API服务器",
    "action_json": "{'action': 'stats-query-mcp_mcp_tool_setup_api_connection', 'action_input': {'url': 'https://***************:8080/'}}"
  },
  {
    "step": 2,
    "description": "查询netlink配置获取链路一的ID信息",
    "action_json": "{'action': 'stats-query-mcp_mcp_tool_get_config', 'action_input': {'config_type': 'netlink', 'netlink_id': 64}}"
  }
]
```
"""
        return prompt

    async def _call_llm_for_step_summary(self, prompt: str) -> List[Dict[str, Any]]:
        """调用LLM生成步骤总结"""
        # 检查是否启用模拟模式
        if self.experience_config.get("use_mock_llm", False):
            return self._mock_llm_response(prompt)

        try:
            print(f"[EXPERIENCE] 准备调用LLM API")
            headers = {
                "Authorization": f"Bearer {self.embedding_config['api_key']}",
                "Content-Type": "application/json"
            }

            data = {
                "model": "glm-4",  # 使用智谱AI的模型
                "messages": [
                    {
                        "role": "system",
                        "content": "你是一个专业的网络统计分析专家，擅长总结技术操作步骤。请准确分析每个步骤的具体作用。"
                    },
                    {"role": "user", "content": prompt}
                ],
                "temperature": 0.1,
                "max_tokens": 2000
            }

            # 使用智谱AI的API端点
            api_url = self.embedding_config.get("chat_api_url", "https://open.bigmodel.cn/api/paas/v4/chat/completions")
            print(f"[EXPERIENCE] API URL: {api_url}")
            print(f"[EXPERIENCE] 提示词长度: {len(prompt)}")

            response = requests.post(
                api_url,
                headers=headers,
                json=data,
                timeout=30
            )

            print(f"[EXPERIENCE] API响应状态码: {response.status_code}")

            if response.status_code == 200:
                result = response.json()
                content = result["choices"][0]["message"]["content"]
                print(f"[EXPERIENCE] LLM响应内容: {content[:200]}...")

                # 解析JSON响应
                import re
                json_match = re.search(r'```json\n(.*?)\n```', content, re.DOTALL)
                if json_match:
                    json_str = json_match.group(1)
                    parsed_result = json.loads(json_str)
                    print(f"[EXPERIENCE] 成功解析JSON，步骤数: {len(parsed_result)}")
                    return parsed_result
                else:
                    # 尝试直接解析
                    try:
                        parsed_result = json.loads(content)
                        print(f"[EXPERIENCE] 直接解析JSON成功，步骤数: {len(parsed_result)}")
                        return parsed_result
                    except Exception as parse_error:
                        self.logger.warning(f"LLM响应解析失败: {content}")
                        print(f"[EXPERIENCE] JSON解析失败: {parse_error}")
                        return []
            else:
                error_msg = f"LLM API调用失败: {response.status_code}, {response.text}"
                self.logger.error(error_msg)
                print(f"[EXPERIENCE] {error_msg}")
                return []

        except Exception as e:
            error_msg = f"调用LLM失败: {e}"
            self.logger.error(error_msg)
            print(f"[EXPERIENCE] {error_msg}")
            import traceback
            traceback.print_exc()
            return []

    def _mock_llm_response(self, prompt: str) -> List[Dict[str, Any]]:
        """模拟LLM响应，用于测试"""
        print(f"[EXPERIENCE] 使用模拟LLM响应")

        # 分析提示词中的工具调用
        mock_summaries = []
        step_num = 1

        if "setup_api_connection" in prompt:
            mock_summaries.append({
                "step": step_num,
                "description": "登录到***************的API服务器",
                "action_json": "{'action': 'stats-query-mcp_mcp_tool_setup_api_connection', 'action_input': {'url': 'https://***************:8080/'}}"
            })
            step_num += 1

        if "get_config" in prompt:
            mock_summaries.append({
                "step": step_num,
                "description": "查询netlink配置获取链路一的ID信息",
                "action_json": "{'action': 'stats-query-mcp_mcp_tool_get_config', 'action_input': {'config_type': 'netlink', 'netlink_id': 64}}"
            })
            step_num += 1

        if "query_statistics_table" in prompt:
            mock_summaries.append({
                "step": step_num,
                "description": "查询summary表获取指定时间段的统计数据",
                "action_json": "{'action': 'stats-query-mcp_mcp_tool_query_statistics_table', 'action_input': {'table': 'summary', 'begintime': '2025-07-17 00:00:00', 'endtime': '2025-07-17 23:59:59', 'fields': 'total_byte,total_packet,total_bitps', 'keys': 'time', 'netlink': 1}}"
            })

        return mock_summaries






    def _extract_key_result(self, final_answer: str) -> str:
        """提取关键结果信息

        Args:
            final_answer: 完整的最终答案

        Returns:
            提取的关键结果
        """
        try:
            # 尝试提取成功结果的关键部分
            if '"success": true' in final_answer:
                import re
                # 提取data字段的内容
                data_match = re.search(r'"data":\s*"([^"]*)"', final_answer)
                if data_match:
                    data_content = data_match.group(1)
                    if len(data_content) > 100:
                        data_content = data_content[:100] + "..."
                    return f'{{"success": true, "error_code": 0, "message": "查询成功", "data": "{data_content}"}}'

            # 如果没有找到特定格式，返回截断的版本
            return final_answer[:200] + "..."

        except Exception:
            return final_answer[:200] + "..."
