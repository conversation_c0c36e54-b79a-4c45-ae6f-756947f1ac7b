#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试LLM步骤总结器
"""

import asyncio
import json
from llm_step_summarizer import LLMStepSummarizer
from experience_manager import ExperienceManager


async def test_llm_summarizer():
    """测试LLM步骤总结器"""
    print("=" * 60)
    print("测试LLM步骤总结器")
    print("=" * 60)
    
    # 初始化总结器
    try:
        summarizer = LLMStepSummarizer()
        print("✓ LLM步骤总结器初始化成功")
    except Exception as e:
        print(f"✗ LLM步骤总结器初始化失败: {e}")
        return
    
    # 检查是否有经验数据
    if not summarizer.experience_manager.experiences:
        print("✗ 没有找到经验数据，请先运行一些查询生成经验")
        return
    
    # 选择第一个成功的经验进行测试
    test_experience = None
    for exp in summarizer.experience_manager.experiences:
        if exp.success and exp.iterations:
            test_experience = exp
            break
    
    if not test_experience:
        print("✗ 没有找到包含迭代步骤的成功经验")
        return
    
    print(f"✓ 找到测试经验: {test_experience.query[:50]}...")
    print(f"  - 执行时间: {test_experience.execution_time:.2f}s")
    print(f"  - 迭代次数: {test_experience.total_iterations}")
    print(f"  - 使用工具: {len(test_experience.tools_used)}")
    
    # 测试步骤总结
    print("\n测试步骤总结...")
    try:
        step_summaries = await summarizer.summarize_experience_steps(test_experience)
        
        if step_summaries:
            print("✓ 步骤总结生成成功:")
            for summary in step_summaries:
                print(f"  {summary['step']}. {summary['description']}")
                print(f"     {summary['action_json']}")
        else:
            print("✗ 步骤总结生成失败")
            return
            
    except Exception as e:
        print(f"✗ 步骤总结生成异常: {e}")
        return
    
    # 测试完整few-shot示例生成
    print("\n测试完整few-shot示例生成...")
    try:
        improved_example = await summarizer.generate_improved_few_shot_example(test_experience, 1.0)
        
        print("✓ 改进的few-shot示例生成成功:")
        print("-" * 60)
        print(improved_example)
        print("-" * 60)
        
    except Exception as e:
        print(f"✗ few-shot示例生成异常: {e}")
        return
    
    # 对比传统方法
    print("\n对比传统方法...")
    try:
        traditional_example = summarizer.experience_manager.generate_detailed_few_shot_example(test_experience, 1.0)
        
        print("传统方法生成的示例:")
        print("-" * 60)
        print(traditional_example)
        print("-" * 60)
        
    except Exception as e:
        print(f"传统方法生成异常: {e}")
    
    print("\n" + "=" * 60)
    print("测试完成")
    print("=" * 60)


async def test_batch_improvement():
    """测试批量改进功能"""
    print("=" * 60)
    print("测试批量改进功能")
    print("=" * 60)
    
    summarizer = LLMStepSummarizer()
    
    # 只改进前3个经验（避免API调用过多）
    limited_experiences = []
    count = 0
    for exp in summarizer.experience_manager.experiences:
        if exp.success and exp.iterations and count < 3:
            limited_experiences.append(exp)
            count += 1
    
    if not limited_experiences:
        print("没有找到可改进的经验")
        return
    
    print(f"找到 {len(limited_experiences)} 个经验进行改进")
    
    # 临时替换经验列表
    original_experiences = summarizer.experience_manager.experiences
    summarizer.experience_manager.experiences = limited_experiences
    
    try:
        improved = await summarizer.batch_improve_experiences("test_improved_experiences.json")
        print(f"✓ 成功改进 {len(improved)} 个经验")
        
        # 显示第一个改进的示例
        if improved:
            print("\n第一个改进的示例:")
            print("-" * 60)
            print(improved[0]["improved_few_shot"])
            print("-" * 60)
            
    except Exception as e:
        print(f"✗ 批量改进失败: {e}")
    finally:
        # 恢复原始经验列表
        summarizer.experience_manager.experiences = original_experiences


def compare_methods():
    """对比不同方法的输出"""
    print("=" * 60)
    print("对比不同方法的输出")
    print("=" * 60)
    
    em = ExperienceManager()
    
    if not em.experiences:
        print("没有经验数据可供对比")
        return
    
    # 选择第一个有效经验
    test_exp = None
    for exp in em.experiences:
        if exp.success and exp.iterations:
            test_exp = exp
            break
    
    if not test_exp:
        print("没有找到有效的经验数据")
        return
    
    print(f"测试经验: {test_exp.query}")
    print(f"迭代步骤数: {len(test_exp.iterations)}")
    
    # 传统方法
    print("\n=== 传统方法 ===")
    traditional = em.generate_detailed_few_shot_example(test_exp, 1.0)
    print(traditional)
    
    # 显示原始迭代数据
    print("\n=== 原始迭代数据 ===")
    for i, iteration in enumerate(test_exp.iterations, 1):
        print(f"迭代 {i}:")
        print(f"  思考: {iteration.thought[:100]}...")
        print(f"  动作: {iteration.action}")
        print(f"  观察: {iteration.observation[:100]}...")
        if iteration.tool_executions:
            for tool_exec in iteration.tool_executions:
                print(f"  工具: {tool_exec.tool_name}")
                print(f"  参数: {tool_exec.parameters}")
        print()


async def main():
    """主函数"""
    import sys
    
    if len(sys.argv) < 2:
        print("用法:")
        print("  python test_llm_summarizer.py test      # 测试LLM总结器")
        print("  python test_llm_summarizer.py batch     # 测试批量改进")
        print("  python test_llm_summarizer.py compare   # 对比不同方法")
        return
    
    command = sys.argv[1]
    
    if command == "test":
        await test_llm_summarizer()
    elif command == "batch":
        await test_batch_improvement()
    elif command == "compare":
        compare_methods()
    else:
        print("无效的命令")


if __name__ == "__main__":
    asyncio.run(main())
